<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdtbu.edu.mapper.CategoryMapper">

    <!-- 获取所有分类列表 -->
    <select id="selectAllCategories" resultType="cn.sdtbu.edu.entity.Category">
        SELECT id, name, description, created_at as createdAt FROM categories ORDER BY created_at ASC
    </select>

    <!-- 根据名称查找分类 -->
    <select id="selectByName" resultType="cn.sdtbu.edu.entity.Category">
        SELECT id, name, description, created_at as createdAt FROM categories WHERE name = #{name}
    </select>

</mapper>
