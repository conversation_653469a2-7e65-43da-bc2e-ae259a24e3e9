<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdtbu.edu.mapper.VerificationCodeMapper">

    <!-- 根据邮箱查找最新的验证码 -->
    <select id="findTopByEmailOrderByCreatedAtDesc" resultType="cn.sdtbu.edu.entity.VerificationCode">
        SELECT * FROM verification_codes WHERE email = #{email} ORDER BY created_at DESC LIMIT 1
    </select>

    <!-- 删除过期的验证码 -->
    <delete id="deleteByExpiresAtBefore">
        DELETE FROM verification_codes WHERE expires_at &lt; #{timestamp}
    </delete>

</mapper>
